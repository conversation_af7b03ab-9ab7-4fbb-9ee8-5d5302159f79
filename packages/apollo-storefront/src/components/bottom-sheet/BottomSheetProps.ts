import { HTMLAttributes, PropsWithChildren, ReactNode } from "react"

export type SnapPointProps = {
  headerHeight: number
  contentHeight: number
  height: number
}

export type snapPoints = (props: SnapPointProps) => number[] | number

export type BottomSheetProps = PropsWithChildren<{
  open?: boolean
  onOpenChange?: (open: boolean) => void
  title?: ReactNode
  closeIcon?: ReactNode
  onClose?: () => void
  snapPoints?: number[] | number
  defaultSnap?: number
  allowContentDrag?: boolean
}> & HTMLAttributes<HTMLDivElement>