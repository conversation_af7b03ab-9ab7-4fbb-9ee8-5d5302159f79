.bottomSheetRoot {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    opacity: 0;
    pointer-events: none;
    align-items: center;
    flex-direction: column;
    justify-content: flex-end;
    transition: 0.1s linear;
    z-index: 400;

    &[data-open] {
        opacity: 1;
        pointer-events: auto;
    }
}

.bottomSheetBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--apl-alias-color-effects-overlay-surface-black, rgba(0, 0, 0, 0.20));
}

.bottomSheetContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    width: 100%;
    position: relative;
    max-height: 90vh;
    border-radius: var(--apl-alias-radius-radius8, 16px) var(--apl-alias-radius-radius8, 16px) var(--apl-alias-radius-radius1, 0) var(--apl-alias-radius-radius1, 0);
    background: var(--apl-alias-color-background-and-surface-background, #FFF);
    box-shadow: var(--apl-alias-elevation-elevations1-x-axis, 0) var(--apl-alias-elevation-elevations1-y-axis, 2px) var(--apl-alias-elevation-elevations1-blur, 4px) var(--apl-alias-elevation-elevations1-spread, 0) var(--apl-alias-elevation-elevations1-color, rgba(0, 0, 0, 0.10));
    transition: 0.3s ease;
    transform: translate3d(0, 100%, 0);

    &[data-open] {
        transform: translate3d(0, 0, 0);
    }
}

.bottomSheetDragging {
    transition: none;
}

.bottomSheetDragging .bottomSheetContainer {
    transition: none;
}

.bottomSheetDragger {
    width: 68px;
    height: 4px;
    background-color: var(--apl-alias-color-outline-and-border-outline-variant, #C8C6C6);
    cursor: ns-resize;
    margin: 0 auto;
    border-radius: 2px;
    transition: background-color 0.2s ease;
    touch-action: none;
    user-select: none;
}

.bottomSheetHeaderContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: var(--apl-alias-spacing-padding-padding5, 8px) var(--apl-alias-spacing-padding-padding8, 16px);
    gap: var(--apl-alias-spacing-gap-gap3, 4px);
}

.bottomSheetHeader {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    gap: var(--apl-alias-spacing-gap-gap7, 12px);
    justify-content: space-between;

    :global(.ApolloTypography--root) {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }
}

.bottomSheetCloseButton {
    color: var(--apl-alias-color-background-and-surface-on-surface);

    &:enabled,
    &[href] {
        &:hover {
            background-color: transparent;
        }
    }
}

.bottomSheetContent {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    flex: 1 0 0;
    padding: var(--apl-alias-spacing-padding-padding1, 0) var(--apl-alias-spacing-padding-padding8, 16px) var(--apl-alias-spacing-padding-padding8, 16px) var(--apl-alias-spacing-padding-padding8, 16px);
}